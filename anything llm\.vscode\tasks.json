{"version": "2.0.0", "tasks": [{"label": "📦 تثبيت المتطلبات", "type": "shell", "command": "python", "args": ["install_dependencies.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🧪 اختبار النظام", "type": "shell", "command": "python", "args": ["test_system.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "dependsOn": "📦 تثبيت المتطلبات"}, {"label": "🚀 تشغيل النظام الذكي (كامل)", "type": "shell", "command": "docker-compose", "args": ["up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "⚡ تشغيل سريع (صور جاهزة)", "type": "shell", "command": "docker-compose", "args": ["-f", "docker-compose-simple.yml", "up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "⏹️ إيقاف النظام الذكي", "type": "shell", "command": "docker-compose", "args": ["down"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🔍 فحص حالة النظام", "type": "shell", "command": "python", "args": ["vscode_ai_controller.py", "--check-status"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🤖 طلب بسيط من AI", "type": "shell", "command": "python", "args": ["vscode_ai_controller.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "📦 تحميل نماذج Ollama إضافية", "type": "shell", "command": "docker-compose", "args": ["exec", "ollama", "ollama", "pull", "${input:modelName}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "📊 عرض النماذج المتاحة", "type": "shell", "command": "docker-compose", "args": ["exec", "ollama", "ollama", "list"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🌐 فتح واجهة n8n", "type": "shell", "command": "start", "args": ["http://localhost:5678"], "windows": {"command": "start", "args": ["http://localhost:5678"]}, "linux": {"command": "xdg-open", "args": ["http://localhost:5678"]}, "osx": {"command": "open", "args": ["http://localhost:5678"]}, "group": "test", "presentation": {"echo": true, "reveal": "silent"}}, {"label": "💬 فتح AnythingLLM", "type": "shell", "command": "start", "args": ["http://localhost:3001"], "windows": {"command": "start", "args": ["http://localhost:3001"]}, "linux": {"command": "xdg-open", "args": ["http://localhost:3001"]}, "osx": {"command": "open", "args": ["http://localhost:3001"]}, "group": "test", "presentation": {"echo": true, "reveal": "silent"}}, {"label": "🔧 إعداد Google Cloud", "type": "shell", "command": "gcloud", "args": ["auth", "login"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "options": {"cwd": "${workspaceFolder}"}}], "inputs": [{"id": "modelName", "description": "اسم النموذج للتحميل", "default": "mixtral", "type": "pickString", "options": ["mixtral", "codellama:13b-instruct", "gemma:7b", "llama3:70b", "mistral:7b", "neural-chat", "starcode"]}, {"id": "userPrompt", "description": "اكتب طلبك للذكاء الاصطناعي", "default": "اكتب دالة Python بسيطة", "type": "promptString"}]}