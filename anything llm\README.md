# 🤖 النظام الذكي المتكامل للتطوير
## AI Integrated Development System

نظام ذكي متطور يجمع بين قوة النماذج المحلية والسحابية لمساعدتك في التطوير والبرمجة من داخل VS Code.

## 🎯 الميزات الرئيسية

### 💡 ذكاء متعدد المستويات
- **مهام بسيطة**: نماذج Ollama المحلية (مجاني وسريع)
- **مهام معقدة**: Gemini Pro + البحث العميق (Google Cloud)
- **ذاكرة المشروع**: AnythingLLM للسياق والمراجع

### 🔗 تكامل شامل
- **VS Code**: واجهة التطوير الأساسية
- **GitHub Copilot**: مساعد الكود الفوري
- **n8n**: تنسيق وأتمتة المهام المعقدة
- **Google Cloud**: قوة الذكاء الاصطناعي السحابي

### 💰 كفاءة في التكلفة
- استخدام ذكي للموارد (محلي أولاً)
- مراقبة الإنفاق والحدود اليومية
- تقدير التكلفة لكل عملية

## 🚀 البدء السريع

### المتطلبات الأساسية
```bash
# تأكد من تثبيت هذه الأدوات:
- Docker Desktop
- Python 3.8+
- Google Cloud SDK (للميزات السحابية)
- VS Code
```

### 1. تشغيل النظام
```bash
# في VS Code، اضغط Ctrl+Shift+P ثم:
Tasks: Run Task > 🚀 تشغيل النظام الذكي

# أو من الطرفية:
docker-compose up -d
```

### 2. فحص الحالة
```bash
# من VS Code:
Tasks: Run Task > 🔍 فحص حالة النظام

# أو من الطرفية:
python vscode_ai_controller.py --check-status
```

### 3. أول طلب ذكي
```bash
# من الطرفية:
python vscode_ai_controller.py "اكتب دالة Python لحساب العدد الأولي"

# أو استخدم VS Code Tasks لواجهة تفاعلية
```

## 🛠️ إعداد Google Cloud (للميزات المتقدمة)

### 1. إنشاء المشروع
```bash
# تسجيل الدخول
gcloud auth login

# إنشاء مشروع
gcloud projects create ai-integrated-system-2025 --name="AI Integrated System"
gcloud config set project ai-integrated-system-2025
```

### 2. تفعيل الخدمات
```bash
gcloud services enable aiplatform.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable storage.googleapis.com
```

### 3. إعداد المصادقة
```bash
# إنشاء حساب خدمة
gcloud iam service-accounts create ai-system-service \
    --description="Service account for AI integrated system"

# تنزيل مفتاح المصادقة
gcloud iam service-accounts keys create ./service-account-key.json \
    --iam-account=<EMAIL>
```

## 📋 أوامر VS Code المتاحة

يمكنك الوصول لهذه الأوامر عبر `Ctrl+Shift+P` ثم `Tasks: Run Task`:

| الأمر | الوصف | الاستخدام |
|-------|--------|-----------|
| 🚀 تشغيل النظام الذكي | تشغيل جميع الخدمات | للبدء |
| ⏹️ إيقاف النظام الذكي | إيقاف جميع الخدمات | عند الانتهاء |
| 🔍 فحص حالة النظام | عرض حالة الخدمات والنماذج | للتشخيص |
| 🤖 طلب بسيط من AI | إرسال طلب للذكاء الاصطناعي | للاستخدام السريع |
| 📦 تحميل نماذج إضافية | تحميل نماذج جديدة في Ollama | توسيع القدرات |
| 🌐 فتح واجهة n8n | فتح واجهة n8n في المتصفح | إدارة الأتمتة |
| 💬 فتح AnythingLLM | فتح واجهة AnythingLLM | إدارة الذاكرة |

## 🎮 طرق الاستخدام

### 1. الاستخدام البسيط (محلي فقط)
```python
# من الطرفية
python vscode_ai_controller.py "اشرح مفهوم الخوارزميات"
```

### 2. الاستخدام المتقدم (عبر AnythingLLM)
1. افتح AnythingLLM في المتصفح: `http://localhost:3001`
2. ارفع ملفات مشروعك للذاكرة
3. اطلب: "ابدأ سير عمل تطوير واجهة React بناءً على المتطلبات المرفوعة"

### 3. الاستخدام الاحترافي (n8n workflows)
1. افتح n8n: `http://localhost:5678`
2. أنشئ مسار عمل مخصص
3. اربطه بـ webhook لاستدعائه من VS Code

## 🧠 النماذج المتاحة والأدوار

### النماذج المحلية (Ollama)
- **llama3:8b**: الأساسي للمهام العامة والبرمجة
- **mistral**: سريع للمهام البسيطة
- **codellama:13b-instruct**: متخصص في البرمجة
- **mixtral**: قوي للتحليل والمراجعة

### النماذج السحابية
- **Gemini Pro 1.5**: للبحث العميق والتفكير الاستراتيجي
- **Gemini CLI**: للاستخدام المباشر من الطرفية

## 📊 مراقبة الاستخدام والتكلفة

### الحدود اليومية الافتراضية
- التكلفة: $5 يومياً
- الرموز: 10,000 رمز يومياً

### تخصيص الحدود
```python
# في vscode_ai_controller.py
self.daily_cost_limit = 10.0  # رفع الحد إلى $10
self.daily_token_limit = 20000  # رفع الحد إلى 20K رمز
```

### عرض الإحصائيات
```bash
python vscode_ai_controller.py --check-status
```

## 🔧 الإعدادات المتقدمة

### إضافة نماذج جديدة
```bash
# تحميل نموذج جديد
docker-compose exec ollama ollama pull neural-chat

# أو استخدم VS Code Task
```

### ربط قاعدة بيانات خارجية
```yaml
# في docker-compose.yml
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ai_system
      POSTGRES_USER: ai_user
      POSTGRES_PASSWORD: secure_password
```

### إعداد HTTPS
```bash
# إنشاء شهادات SSL
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/nginx.key \
    -out nginx/ssl/nginx.crt
```

## 🚨 استكشاف الأخطاء

### الخدمات لا تعمل
```bash
# إعادة تشغيل كل شيء
docker-compose down
docker-compose up -d

# فحص logs
docker-compose logs -f
```

### مشاكل Google Cloud
```bash
# التحقق من المصادقة
gcloud auth list

# إعادة تسجيل الدخول
gcloud auth login

# فحص الحصص
gcloud compute project-info describe --project=ai-integrated-system-2025
```

### مشاكل النماذج
```bash
# إعادة تحميل نموذج
docker-compose exec ollama ollama pull llama3

# فحص المساحة المتاحة
docker system df
```

## 📁 هيكل المشروع

```
ai-integrated-system/
├── 📄 README.md                    # هذا الملف
├── 📄 integrated_ai_system_plan.md # الخطة الشاملة
├── 📄 google_cloud_setup.md        # إعداد Google Cloud
├── 📄 docker-compose.yml           # إعداد الخدمات
├── 📄 vscode_ai_controller.py      # المتحكم الرئيسي
├── 📁 .vscode/
│   └── 📄 tasks.json               # مهام VS Code
├── 📁 anything_llm_config/         # إعدادات AnythingLLM
├── 📁 n8n_workflows/               # مسارات عمل n8n
└── 📄 service-account-key.json     # مفتاح Google Cloud
```

## 🤝 المساهمة والتطوير

### إضافة ميزات جديدة
1. Fork المشروع
2. أنشئ branch للميزة: `git checkout -b feature/amazing-feature`
3. Commit التغييرات: `git commit -m 'Add amazing feature'`
4. Push إلى branch: `git push origin feature/amazing-feature`
5. افتح Pull Request

### اقتراح تحسينات
- افتح Issue جديد
- وصف التحسين المطلوب
- أرفق أمثلة أو screenshots

## 📞 الدعم والمساعدة

### الموارد المفيدة
- [وثائق Ollama](https://ollama.ai/docs)
- [وثائق n8n](https://docs.n8n.io)
- [وثائق AnythingLLM](https://docs.anythingllm.com)
- [وثائق Google Cloud AI](https://cloud.google.com/ai)

### التواصل
- GitHub Issues للمشاكل التقنية
- Discussions للأسئلة العامة

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- فريق Ollama لتوفير النماذج المحلية
- فريق n8n لمنصة الأتمتة الرائعة
- فريق AnythingLLM لحل إدارة المستندات
- Google لتوفير Gemini Pro API
- Microsoft لـ VS Code والدعم المستمر

---

**تم إنشاء هذا النظام بحب ❤️ للمطورين الذين يحبون الذكاء الاصطناعي والأتمتة**

🚀 ابدأ رحلتك مع الذكاء الاصطناعي اليوم!
