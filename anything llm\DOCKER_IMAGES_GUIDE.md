# 🐳 دليل استخدام الصور الجاهزة
## Docker Images Guide - AnythingLLM System

## 🎯 خيارات التشغيل المتاحة

### 1. التشغيل السريع (الموصى به) ⚡
```bash
# استخدام الصور الجاهزة فقط
docker-compose -f docker-compose-simple.yml up -d
```

### 2. التشغيل الكامل 🚀
```bash
# النظام المتكامل مع جميع الخدمات
docker-compose up -d
```

## 📦 الصور المستخدمة

### AnythingLLM الرسمية
- **الصورة**: `mintplexlabs/anythingllm:latest`
- **المنفذ**: 3001
- **الحجم**: ~2.5 GB
- **الميزات**: 
  - واجهة ويب كاملة
  - دعم متعدد النماذج
  - إدارة المستندات
  - MCP Server support

### Ollama للنماذج المحلية
- **الصورة**: `ollama/ollama:latest`
- **المنفذ**: 11434
- **الحجم**: ~500 MB (بدون نماذج)
- **الميزات**:
  - تشغيل النماذج محلياً
  - API RESTful
  - دعم GPU اختياري

### n8n للأتمتة
- **الصورة**: `n8nio/n8n:latest`
- **المنفذ**: 5678
- **الحجم**: ~300 MB
- **الميزات**:
  - أتمتة العمليات
  - واجهة بصرية
  - تكامل مع APIs

## 🚀 التشغيل السريع خطوة بخطوة

### الخطوة 1: تنزيل الصور
```bash
# تنزيل الصور الأساسية
docker pull mintplexlabs/anythingllm:latest
docker pull ollama/ollama:latest
docker pull n8nio/n8n:latest
```

### الخطوة 2: تشغيل النظام
```bash
# من VS Code
Ctrl+Shift+P > Tasks: Run Task > ⚡ تشغيل سريع (صور جاهزة)

# أو من الطرفية
docker-compose -f docker-compose-simple.yml up -d
```

### الخطوة 3: التحقق من التشغيل
```bash
# فحص الحاويات
docker ps

# فحص logs إذا لزم الأمر
docker-compose -f docker-compose-simple.yml logs -f
```

### الخطوة 4: الوصول للواجهات
- **AnythingLLM**: http://localhost:3001
- **n8n**: http://localhost:5678 (admin/change-this-password)
- **Ollama API**: http://localhost:11434

## 📋 أوامر الإدارة

### بدء التشغيل
```bash
# تشغيل في الخلفية
docker-compose -f docker-compose-simple.yml up -d

# تشغيل مع عرض logs
docker-compose -f docker-compose-simple.yml up
```

### إيقاف النظام
```bash
# إيقاف الحاويات
docker-compose -f docker-compose-simple.yml down

# إيقاف مع حذف البيانات
docker-compose -f docker-compose-simple.yml down -v
```

### إدارة النماذج
```bash
# دخول إلى Ollama
docker-compose -f docker-compose-simple.yml exec ollama bash

# تحميل نموذج
docker-compose -f docker-compose-simple.yml exec ollama ollama pull llama3

# عرض النماذج المثبتة
docker-compose -f docker-compose-simple.yml exec ollama ollama list
```

## 🔧 التخصيص والإعدادات

### إعدادات AnythingLLM
```yaml
# في docker-compose-simple.yml
environment:
  - JWT_SECRET=change-this-to-a-secure-random-string
  - DISABLE_TELEMETRY=true
  - OLLAMA_BASE_URL=http://ollama:11434
```

### إعدادات n8n
```yaml
# في docker-compose-simple.yml
environment:
  - N8N_BASIC_AUTH_USER=admin
  - N8N_BASIC_AUTH_PASSWORD=change-this-password
  - GENERIC_TIMEZONE=Africa/Cairo
```

### دعم GPU (اختياري)
```yaml
# إزالة التعليق في docker-compose-simple.yml
deploy:
  resources:
    reservations:
      devices:
        - driver: nvidia
          count: 1
          capabilities: [gpu]
```

## 📊 مقارنة الخيارات

| الخاصية | التشغيل السريع | التشغيل الكامل |
|---------|----------------|-----------------|
| وقت البدء | 2-3 دقائق | 5-10 دقائق |
| استهلاك الذاكرة | 3-4 GB | 6-8 GB |
| الميزات | أساسية | متكاملة |
| التعقيد | بسيط | متقدم |
| الاستخدام | للبداية | للإنتاج |

## 🔍 استكشاف الأخطاء

### مشكلة: AnythingLLM لا يبدأ
```bash
# فحص logs
docker-compose -f docker-compose-simple.yml logs anythingllm

# إعادة تشغيل
docker-compose -f docker-compose-simple.yml restart anythingllm
```

### مشكلة: Ollama لا يستجيب
```bash
# فحص الحالة
curl http://localhost:11434/api/tags

# إعادة تشغيل
docker-compose -f docker-compose-simple.yml restart ollama
```

### مشكلة: نفاد المساحة
```bash
# حذف البيانات غير المستخدمة
docker system prune -f

# حذف الصور القديمة
docker image prune -f

# فحص الاستخدام
docker system df
```

### مشكلة: تضارب المنافذ
```bash
# فحص المنافذ المستخدمة
netstat -an | findstr :3001
netstat -an | findstr :11434
netstat -an | findstr :5678

# تغيير المنافذ في docker-compose-simple.yml
```

## 🎯 أفضل الممارسات

### 1. الأمان
```bash
# غير كلمات المرور الافتراضية
JWT_SECRET=your-secure-random-string
N8N_BASIC_AUTH_PASSWORD=your-secure-password
```

### 2. النسخ الاحتياطية
```bash
# نسخ احتياطي للبيانات
docker run --rm -v anythingllm_storage:/data -v $(pwd):/backup alpine tar czf /backup/anythingllm-backup.tar.gz -C /data .
```

### 3. التحديثات
```bash
# تحديث الصور
docker-compose -f docker-compose-simple.yml pull
docker-compose -f docker-compose-simple.yml up -d
```

### 4. المراقبة
```bash
# مراقبة الموارد
docker stats

# مراقبة logs
docker-compose -f docker-compose-simple.yml logs -f --tail=100
```

## 📚 الموارد المفيدة

### وثائق رسمية:
- [AnythingLLM Documentation](https://docs.anythingllm.com)
- [Ollama Documentation](https://ollama.ai/docs)
- [n8n Documentation](https://docs.n8n.io)

### صور Docker:
- [AnythingLLM on Docker Hub](https://hub.docker.com/r/mintplexlabs/anythingllm)
- [Ollama on Docker Hub](https://hub.docker.com/r/ollama/ollama)
- [n8n on Docker Hub](https://hub.docker.com/r/n8nio/n8n)

## 🎉 الخطوات التالية

1. **ابدأ بالتشغيل السريع** للتعرف على النظام
2. **حمّل بعض النماذج** في Ollama (llama3, mistral)
3. **ارفع مستندات** إلى AnythingLLM
4. **أنشئ مسارات عمل** في n8n
5. **انتقل للتشغيل الكامل** عند الحاجة لمزيد من الميزات

---

**🚀 استمتع بنظامك الذكي الجديد مع الصور الجاهزة!**
