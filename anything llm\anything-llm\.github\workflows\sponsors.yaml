name: Generate Sponsors README

on:
  schedule:
    - cron: "0 12 * * 3" # Run every Wednesday at 12:00 PM UTC

permissions:
  contents: write
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v2

      - name: Generate All Sponsors README
        id: generate-all-sponsors
        uses: JamesIves/github-sponsors-readme-action@v1
        with:
          token: ${{ secrets.SPONSOR_PAT }}
          file: 'README.md'
          organization: true
          active-only: false
          marker: 'all-sponsors'

      - name: Commit and Push 🚀
        uses: stefanzweifel/git-auto-commit-action@v5
        id: auto-commit-action
        with:
          commit_message: 'Update Sponsors README'
          file_pattern: 'README.md'

      - name: Generate PR if changes detected
        uses: peter-evans/create-pull-request@v7
        if: steps.auto-commit-action.outputs.files_changed == 'true'
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          title: 'Update Sponsors README'
          branch: 'chore/update-sponsors'
          base: 'master'
          draft: false
          reviewers: 'timothy<PERSON><PERSON>bat'
          assignees: 'timothycarambat'
          maintainer-can-modify: true