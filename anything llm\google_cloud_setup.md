# إعداد Google Cloud للنظام الذكي المتكامل

## المرحلة الأولى: إعداد المشروع وقاعدة البيانات

### 1. إن<PERSON>اء المشروع
```bash
# تثبيت Google Cloud SDK (إذا لم يكن مثبتاً)
# تنزيل من: https://cloud.google.com/sdk/docs/install

# تسجيل الدخول
gcloud auth login

# إنشاء مشروع جديد
gcloud projects create ai-integrated-system-2025 --name="AI Integrated System"

# تعيين المشروع الافتراضي
gcloud config set project ai-integrated-system-2025

# تفعيل الفوترة (مطلوب لاستخدام APIs)
gcloud billing projects link ai-integrated-system-2025 --billing-account=YOUR_BILLING_ACCOUNT_ID
```

### 2. تفعيل الخدمات المطلوبة
```bash
# تفعيل APIs الأساسية
gcloud services enable aiplatform.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
```

### 3. إعداد قاعدة بيانات Firestore
```bash
# إنشاء قاعدة بيانات Firestore
gcloud firestore databases create --region=us-central1
```

### 4. إنشاء حساب الخدمة للمصادقة
```bash
# إنشاء حساب خدمة
gcloud iam service-accounts create ai-system-service \
    --description="Service account for AI integrated system" \
    --display-name="AI System Service Account"

# منح الصلاحيات المطلوبة
gcloud projects add-iam-policy-binding ai-integrated-system-2025 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding ai-integrated-system-2025 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/datastore.user"

# تنزيل مفتاح المصادقة
gcloud iam service-accounts keys create ./service-account-key.json \
    --iam-account=<EMAIL>
```

## المرحلة الثانية: إعداد Gemini Pro

### 1. تفعيل Vertex AI وGemini
```bash
# التأكد من تفعيل Vertex AI
gcloud services enable aiplatform.googleapis.com

# اختبار الوصول لـ Gemini Pro
gcloud ai models list --region=us-central1 --filter="displayName:gemini"
```

### 2. إعداد مفتاح API للوصول
```python
# test_gemini_connection.py
import vertexai
from vertexai.generative_models import GenerativeModel

# إعداد المشروع
PROJECT_ID = "ai-integrated-system-2025"
REGION = "us-central1"

# تهيئة Vertex AI
vertexai.init(project=PROJECT_ID, location=REGION)

# إنشاء نموذج Gemini Pro
model = GenerativeModel("gemini-1.5-pro")

# اختبار بسيط
def test_gemini():
    try:
        response = model.generate_content("اكتب دالة Python بسيطة لحساب مربع رقم")
        print("✅ Gemini Pro يعمل بنجاح!")
        print(f"الاستجابة: {response.text}")
        return True
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

if __name__ == "__main__":
    test_gemini()
```

### 3. تقدير التكلفة وإعداد الحدود
```bash
# إعداد تنبيهات الفوترة
gcloud billing budgets create \
    --billing-account=YOUR_BILLING_ACCOUNT_ID \
    --display-name="AI System Budget" \
    --budget-amount=500USD \
    --threshold-rules-percent=50,80,100 \
    --threshold-rules-spend-basis=CURRENT_SPEND
```

## هيكل قاعدة البيانات Firestore

### Collections المقترحة:
```javascript
// 1. المشاريع
/projects/{projectId}
{
  name: "اسم المشروع",
  description: "وصف المشروع", 
  created_at: timestamp,
  last_modified: timestamp,
  files: [
    {
      name: "filename.js",
      content: "محتوى الملف",
      language: "javascript"
    }
  ],
  models_used: ["gemini-pro", "llama3", "mistral"],
  total_tokens: 1500,
  cost_estimate: 0.05
}

// 2. المحادثات
/conversations/{conversationId}
{
  project_id: "project123",
  messages: [
    {
      role: "user",
      content: "اكتب دالة لحساب المتوسط",
      timestamp: timestamp,
      source: "vscode" // أو "anythingllm" أو "n8n"
    },
    {
      role: "assistant", 
      content: "function average(numbers) {...}",
      model_used: "gemini-pro",
      tokens_used: 150,
      timestamp: timestamp
    }
  ],
  workflow_triggered: "complex_development",
  total_cost: 0.02
}

// 3. أداء النماذج
/models_performance/{modelId}
{
  model_name: "gemini-pro",
  total_requests: 100,
  average_response_time: 2.5,
  success_rate: 99.5,
  total_tokens: 50000,
  total_cost: 10.25,
  use_cases: {
    "code_generation": 40,
    "research": 30, 
    "analysis": 30
  }
}

// 4. تفضيلات المستخدم
/user_preferences/{userId}
{
  preferred_models: {
    "simple_tasks": "llama3",
    "complex_tasks": "gemini-pro",
    "code_review": "mixtral"
  },
  cost_limits: {
    "daily": 5.0,
    "monthly": 100.0
  },
  notification_settings: {
    "cost_alerts": true,
    "completion_notifications": true
  }
}
```

## خطة توفير التكلفة

### استراتيجية الاستخدام الذكي:
1. **المهام البسيطة (مجاني)**: Ollama محلي
2. **المهام المتوسطة (تكلفة منخفضة)**: Gemini Pro مع prompts محسنة
3. **المهام المعقدة**: Gemini Pro + البحث العميق

### تقدير التكلفة الشهرية:
```
- Gemini Pro 1.5: ~$0.00125 لكل 1K token input, $0.005 لكل 1K token output
- متوسط الاستخدام المتوقع: 500K tokens شهرياً
- التكلفة المتوقعة: ~$15-25 شهرياً
- مدة استهلاك الرصيد: 24-40 شهر
```

### حدود الأمان:
```bash
# إعداد حدود يومية للاستخدام
export DAILY_TOKEN_LIMIT=10000
export DAILY_COST_LIMIT=5.00
export MONTHLY_COST_LIMIT=50.00
```

## الخطوات التالية:
1. ✅ تنفيذ أوامر إعداد Google Cloud
2. ✅ اختبار الاتصال مع Gemini Pro
3. ✅ إعداد قاعدة البيانات وهيكلها
4. ⏳ ربط النظام المحلي مع Google Cloud
5. ⏳ إنشاء مسارات العمل في n8n

هل أنت مستعد لبدء تنفيذ هذه الخطوات؟
