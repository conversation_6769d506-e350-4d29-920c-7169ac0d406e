#!/usr/bin/env python3
"""
سكريبت لاختبار AI Dev Assistant Webhook
"""

import requests
import json
import time

# إعدادات الـ webhook
WEBHOOK_URL = "http://localhost:5678/webhook/a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22"

def test_webhook(feature_request, feature_name="test_feature"):
    """اختبار الـ webhook مع طلب ميزة"""
    
    payload = {
        "feature_request": feature_request,
        "feature_name": feature_name
    }
    
    print(f"🚀 إرسال طلب إلى الـ webhook...")
    print(f"📝 الميزة المطلوبة: {feature_request}")
    print(f"📁 اسم الملف: {feature_name}")
    
    try:
        response = requests.post(
            WEBHOOK_URL,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=120  # مهلة زمنية 2 دقيقة
        )
        
        if response.status_code == 200:
            print("✅ تم إرسال الطلب بنجاح!")
            print(f"📊 Response: {response.text}")
            return True
        else:
            print(f"❌ فشل في إرسال الطلب: {response.status_code}")
            print(f"📊 Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ انتهت المهلة الزمنية للطلب")
        return False
    except Exception as e:
        print(f"❌ خطأ في إرسال الطلب: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار AI Dev Assistant Webhook")
    print("=" * 50)
    
    # أمثلة على طلبات الميزات
    test_cases = [
        {
            "feature_request": "نظام تسجيل دخول للمستخدمين مع التحقق من الهوية باستخدام JWT",
            "feature_name": "user_authentication"
        },
        {
            "feature_request": "صفحة عرض المنتجات مع فلترة وبحث",
            "feature_name": "product_catalog"
        },
        {
            "feature_request": "نظام إدارة المهام مع إمكانية إضافة وحذف وتعديل المهام",
            "feature_name": "task_management"
        }
    ]
    
    print("اختر حالة اختبار:")
    for i, case in enumerate(test_cases, 1):
        print(f"{i}. {case['feature_request'][:50]}...")
    
    print("4. إدخال طلب مخصص")
    
    try:
        choice = input("\nأدخل رقم الاختيار (1-4): ").strip()
        
        if choice in ['1', '2', '3']:
            case = test_cases[int(choice) - 1]
            test_webhook(case['feature_request'], case['feature_name'])
        elif choice == '4':
            feature_request = input("أدخل وصف الميزة المطلوبة: ").strip()
            feature_name = input("أدخل اسم الملف (اختياري): ").strip() or "custom_feature"
            test_webhook(feature_request, feature_name)
        else:
            print("❌ اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n👋 تم إلغاء الاختبار")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
