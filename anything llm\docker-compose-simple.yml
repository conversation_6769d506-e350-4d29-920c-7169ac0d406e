# Docker Compose البسيط - صور جاهزة فقط
# استخدم هذا الملف للبدء السريع مع الصور الجاهزة

services:
  # خدمة Ollama - النماذج المحلية
  ollama:
    image: ollama/ollama:latest
    container_name: ollama_models
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    networks:
      - ai_network
    # لدعم GPU إذا كان متاح (قم بإزالة التعليق)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # خدمة AnythingLLM - الصورة الجاهزة الرسمية
  anythingllm:
    image: mintplexlabs/anythingllm:latest
    container_name: anythingllm
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      # إعدادات أساسية
      - STORAGE_DIR=/app/server/storage
      - SERVER_PORT=3001
      
      # ربط مع Ollama
      - OLLAMA_BASE_URL=http://ollama:11434
      
      # إعدادات الأمان
      - JWT_SECRET=change-this-to-a-secure-random-string
      - DISABLE_TELEMETRY=true
      
      # إعدادات المستخدم
      - UID=1000
      - GID=1000
      
      # إعدادات إضافية
      - INNGEST_EVENT_KEY=background-service-key
      
    volumes:
      # البيانات الرئيسية
      - anythingllm_storage:/app/server/storage
      
      # مجلد الملفات المؤقتة (HotDir)
      - anythingllm_hotdir:/app/collector/hotdir
      
      # مجلد المخرجات
      - anythingllm_outputs:/app/collector/outputs
      
    networks:
      - ai_network
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s

  # خدمة n8n - منصة الأتمتة (اختيارية)
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n_automation
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      # إعدادات أساسية
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      
      # المصادقة (غير كلمة المرور!)
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=change-this-password
      
      # إعدادات إضافية
      - GENERIC_TIMEZONE=Africa/Cairo
      - NODE_ENV=production
      
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - ai_network
    depends_on:
      - ollama
      - anythingllm

volumes:
  # وحدات التخزين المستمرة
  ollama_data:
    driver: local
  anythingllm_storage:
    driver: local
  anythingllm_hotdir:
    driver: local
  anythingllm_outputs:
    driver: local
  n8n_data:
    driver: local

networks:
  ai_network:
    driver: bridge
    name: ai_network
