{"name": "anything-llm-server", "version": "1.8.2", "description": "Server endpoints to process or create content for chatting", "main": "index.js", "author": "<PERSON> (Mintplex Labs)", "license": "MIT", "private": false, "engines": {"node": ">=18.12.1"}, "scripts": {"dev": "cross-env NODE_ENV=development nodemon --ignore documents --ignore vector-cache --ignore storage --ignore swagger --trace-warnings index.js", "start": "cross-env NODE_ENV=production node index.js", "lint": "yarn prettier --ignore-path ../.prettierignore --write ./endpoints ./models ./utils index.js", "swagger": "node ./swagger/init.js"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@aws-sdk/client-bedrock-runtime": "^3.775.0", "@datastax/astra-db-ts": "^0.1.3", "@ladjs/graceful": "^3.2.2", "@lancedb/lancedb": "0.15.0", "@langchain/anthropic": "0.1.16", "@langchain/aws": "^0.0.5", "@langchain/community": "0.0.53", "@langchain/core": "0.1.61", "@langchain/openai": "0.0.28", "@langchain/textsplitters": "0.0.0", "@mintplex-labs/bree": "^9.2.5", "@mintplex-labs/express-ws": "^5.0.7", "@modelcontextprotocol/sdk": "^1.11.0", "@pinecone-database/pinecone": "^2.0.1", "@prisma/client": "5.3.1", "@qdrant/js-client-rest": "^1.9.0", "@xenova/transformers": "^2.14.0", "@zilliz/milvus2-sdk-node": "^2.3.5", "adm-zip": "^0.5.16", "apache-arrow": "19.0.0", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "chalk": "^4", "check-disk-space": "^3.4.0", "cheerio": "^1.0.0", "chromadb": "^2.0.1", "cohere-ai": "^7.9.5", "cors": "^2.8.5", "dotenv": "^16.0.3", "elevenlabs": "^0.5.0", "express": "^4.18.2", "extract-json-from-string": "^1.0.1", "graphql": "^16.7.1", "joi": "^17.11.0", "joi-password-complexity": "^5.2.0", "js-tiktoken": "^1.0.8", "jsonrepair": "^3.7.0", "jsonwebtoken": "^9.0.0", "langchain": "0.1.36", "mime": "^3.0.0", "moment": "^2.29.4", "mssql": "^10.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.8", "ollama": "^0.5.10", "openai": "4.95.1", "pg": "^8.11.5", "pinecone-client": "^1.1.0", "pluralize": "^8.0.0", "posthog-node": "^3.1.1", "prisma": "5.3.1", "slugify": "^1.6.6", "swagger-autogen": "^2.23.5", "swagger-ui-express": "^5.0.0", "truncate": "^3.0.0", "url-pattern": "^1.0.3", "uuid": "^9.0.0", "uuid-apikey": "^1.5.3", "weaviate-ts-client": "^1.4.0", "winston": "^3.13.0"}, "devDependencies": {"@inquirer/prompts": "^4.3.1", "cross-env": "^7.0.3", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-ft-flow": "^3.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "flow-bin": "^0.217.0", "flow-remove-types": "^2.217.1", "globals": "^13.21.0", "hermes-eslint": "^0.15.0", "node-html-markdown": "^1.3.0", "nodemon": "^2.0.22", "prettier": "^3.0.3"}}