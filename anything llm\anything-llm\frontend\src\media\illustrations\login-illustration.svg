<svg width="500" height="656" viewBox="0 0 500 656" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_4)">
<g filter="url(#filter1_ii_1_4)">
<path d="M126.778 581.68V225.373L177.937 256.068V611.774L126.778 581.68Z" fill="url(#paint0_linear_1_4)"/>
</g>
<path d="M127.929 577.98L192.097 616.48L177.693 625.145L112.619 588.534L112.619 220.107L127.817 208.962L127.929 577.98Z" fill="url(#paint1_linear_1_4)"/>
<path d="M176.786 258.588L112.619 220.088L128.154 208.851L192.096 248.034V616.461L177.596 625.326L176.786 258.588Z" fill="url(#paint2_linear_1_4)"/>
<g filter="url(#filter2_ii_1_4)">
<path d="M265.61 514.411V158.104L316.769 188.799V544.505L265.61 514.411Z" fill="url(#paint3_linear_1_4)"/>
</g>
<path d="M266.761 510.711L330.928 549.211L316.525 557.876L251.451 521.266L251.451 152.839L266.648 141.694L266.761 510.711Z" fill="url(#paint4_linear_1_4)"/>
<path d="M315.618 191.32L251.451 152.82L266.986 141.583L330.928 180.765V549.192L316.428 558.057L315.618 191.32Z" fill="url(#paint5_linear_1_4)"/>
<g filter="url(#filter3_ii_1_4)">
<path d="M404.442 418.683V62.3754L455.602 93.071V448.776L404.442 418.683Z" fill="url(#paint6_linear_1_4)"/>
</g>
<path d="M405.594 414.982L469.761 453.483L455.357 462.147L390.283 425.537L390.283 57.11L405.481 45.9652L405.594 414.982Z" fill="url(#paint7_linear_1_4)"/>
<path d="M454.45 95.5913L390.283 57.0911L405.818 45.8542L469.761 85.0366V453.464L455.261 462.328L454.45 95.5913Z" fill="url(#paint8_linear_1_4)"/>
</g>
<rect x="88.956" y="351.304" width="68.0244" height="40.4539" rx="15" fill="url(#paint9_linear_1_4)"/>
<rect x="104.57" y="359.68" width="36.797" height="5.23376" rx="2.61688" fill="white" fill-opacity="0.8"/>
<rect x="104.57" y="378.148" width="36.797" height="5.23376" rx="2.61688" fill="white" fill-opacity="0.8"/>
<rect x="104.57" y="368.914" width="36.797" height="5.23376" rx="2.61688" fill="white" fill-opacity="0.8"/>
<mask id="mask0_1_4" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="211" width="178" height="436">
<rect x="0.787216" y="211.982" width="177.152" height="434.649" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_1_4)">
<rect x="51.503" y="479.103" width="183.106" height="78.9537" rx="39.4769" fill="url(#paint10_linear_1_4)"/>
<circle cx="99.9761" cy="509.549" r="13.9262" fill="white"/>
<circle cx="143.056" cy="519.287" r="13.9262" fill="white"/>
<circle cx="186.136" cy="519.287" r="13.9262" fill="white"/>
</g>
<mask id="mask1_1_4" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="148" y="178" width="169" height="340">
<rect x="148.819" y="178.725" width="167.95" height="338.735" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask1_1_4)">
<rect x="187.512" y="233.079" width="183.106" height="78.9537" rx="39.4769" fill="url(#paint11_linear_1_4)"/>
<path d="M310.535 287.977L305.269 284.227L311.812 275.529L301.997 272.178L303.992 266.034L313.886 269.305V258.613H320.35V269.305L330.244 266.034L332.239 272.178L322.424 275.529L328.888 284.227L323.701 287.977L317.078 279.28L310.535 287.977Z" fill="white"/>
<path d="M270.716 287.977L265.449 284.227L271.992 275.529L262.178 272.178L264.173 266.034L274.067 269.305V258.613H280.53V269.305L290.425 266.034L292.42 272.178L282.605 275.529L289.068 284.227L283.882 287.977L277.259 279.28L270.716 287.977Z" fill="white"/>
<path d="M230.897 287.977L225.63 284.227L232.173 275.529L222.359 272.178L224.354 266.034L234.248 269.305V258.613H240.711V269.305L250.606 266.034L252.601 272.178L242.786 275.529L249.249 284.227L244.063 287.977L237.44 279.28L230.897 287.977Z" fill="white"/>
<rect x="252.529" y="387.811" width="100.24" height="43.2226" rx="21.6113" fill="url(#paint12_linear_1_4)"/>
<circle cx="279.065" cy="404.479" r="7.62378" fill="white"/>
<circle cx="302.649" cy="409.81" r="7.62378" fill="white"/>
</g>
<mask id="mask2_1_4" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="199" y="0" width="257" height="309">
<rect x="199.166" y="0.894867" width="256.435" height="307.227" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask2_1_4)">
<rect x="317.531" y="103.658" width="183.106" height="108.893" rx="40" fill="url(#paint13_linear_1_4)"/>
<rect x="343.093" y="123.945" width="131.983" height="18.7724" rx="8" fill="white" fill-opacity="0.8"/>
<rect x="343.093" y="173.49" width="131.983" height="18.7724" rx="8" fill="white" fill-opacity="0.8"/>
<rect x="343.093" y="148.718" width="131.983" height="18.7724" rx="8" fill="white" fill-opacity="0.8"/>
</g>
<defs>
<filter id="filter0_d_1_4" x="102.619" y="35.8542" width="397.142" height="619.471" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="10" dy="10"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_4" result="shape"/>
</filter>
<filter id="filter1_ii_1_4" x="122.778" y="221.373" width="59.1591" height="394.401" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_4"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="-4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.471302 0 0 0 0 0.547141 0 0 0 0 0.651872 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1_4" result="effect2_innerShadow_1_4"/>
</filter>
<filter id="filter2_ii_1_4" x="261.61" y="154.104" width="59.159" height="394.401" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_4"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="-4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.471302 0 0 0 0 0.547141 0 0 0 0 0.651872 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1_4" result="effect2_innerShadow_1_4"/>
</filter>
<filter id="filter3_ii_1_4" x="400.442" y="58.3754" width="59.159" height="394.401" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_4"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="-4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.471302 0 0 0 0 0.547141 0 0 0 0 0.651872 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1_4" result="effect2_innerShadow_1_4"/>
</filter>
<linearGradient id="paint0_linear_1_4" x1="152.358" y1="225.373" x2="152.358" y2="611.774" gradientUnits="userSpaceOnUse">
<stop stop-color="#41495D"/>
<stop offset="1" stop-color="#293240"/>
</linearGradient>
<linearGradient id="paint1_linear_1_4" x1="152.358" y1="208.962" x2="152.358" y2="625.145" gradientUnits="userSpaceOnUse">
<stop stop-color="#151B23"/>
<stop offset="1" stop-color="#526A89"/>
</linearGradient>
<linearGradient id="paint2_linear_1_4" x1="152.358" y1="211.423" x2="152.358" y2="627.606" gradientUnits="userSpaceOnUse">
<stop stop-color="#697784"/>
<stop offset="1" stop-color="#181B1E"/>
</linearGradient>
<linearGradient id="paint3_linear_1_4" x1="291.189" y1="158.104" x2="291.189" y2="544.505" gradientUnits="userSpaceOnUse">
<stop stop-color="#41495D"/>
<stop offset="1" stop-color="#293240"/>
</linearGradient>
<linearGradient id="paint4_linear_1_4" x1="291.189" y1="141.694" x2="291.189" y2="557.876" gradientUnits="userSpaceOnUse">
<stop stop-color="#151B23"/>
<stop offset="1" stop-color="#526A89"/>
</linearGradient>
<linearGradient id="paint5_linear_1_4" x1="291.19" y1="144.155" x2="291.19" y2="560.337" gradientUnits="userSpaceOnUse">
<stop stop-color="#697784"/>
<stop offset="1" stop-color="#181B1E"/>
</linearGradient>
<linearGradient id="paint6_linear_1_4" x1="430.022" y1="62.3754" x2="430.022" y2="448.776" gradientUnits="userSpaceOnUse">
<stop stop-color="#41495D"/>
<stop offset="1" stop-color="#293240"/>
</linearGradient>
<linearGradient id="paint7_linear_1_4" x1="430.022" y1="45.9652" x2="430.022" y2="462.147" gradientUnits="userSpaceOnUse">
<stop stop-color="#151B23"/>
<stop offset="1" stop-color="#526A89"/>
</linearGradient>
<linearGradient id="paint8_linear_1_4" x1="430.022" y1="48.4262" x2="430.022" y2="464.608" gradientUnits="userSpaceOnUse">
<stop stop-color="#697784"/>
<stop offset="1" stop-color="#181B1E"/>
</linearGradient>
<linearGradient id="paint9_linear_1_4" x1="122.968" y1="351.304" x2="122.968" y2="391.758" gradientUnits="userSpaceOnUse">
<stop stop-color="#46C8FF"/>
<stop offset="0.438941" stop-color="#3AA5D2"/>
<stop offset="1" stop-color="#2A7899"/>
</linearGradient>
<linearGradient id="paint10_linear_1_4" x1="143.056" y1="479.103" x2="143.056" y2="558.057" gradientUnits="userSpaceOnUse">
<stop stop-color="#46C8FF"/>
<stop offset="0.438941" stop-color="#3AA5D2"/>
<stop offset="1" stop-color="#2A7899"/>
</linearGradient>
<linearGradient id="paint11_linear_1_4" x1="279.065" y1="233.079" x2="279.065" y2="312.033" gradientUnits="userSpaceOnUse">
<stop stop-color="#46C8FF"/>
<stop offset="0.438941" stop-color="#3AA5D2"/>
<stop offset="1" stop-color="#2A7899"/>
</linearGradient>
<linearGradient id="paint12_linear_1_4" x1="302.649" y1="387.811" x2="302.649" y2="431.034" gradientUnits="userSpaceOnUse">
<stop stop-color="#46C8FF"/>
<stop offset="0.438941" stop-color="#3AA5D2"/>
<stop offset="1" stop-color="#2A7899"/>
</linearGradient>
<linearGradient id="paint13_linear_1_4" x1="409.084" y1="103.658" x2="409.084" y2="212.55" gradientUnits="userSpaceOnUse">
<stop stop-color="#46C8FF"/>
<stop offset="0.438941" stop-color="#3AA5D2"/>
<stop offset="1" stop-color="#2A7899"/>
</linearGradient>
</defs>
</svg>
