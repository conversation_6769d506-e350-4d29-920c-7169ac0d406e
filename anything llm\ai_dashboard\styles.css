/* إعداد عام للصفحة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

/* شريط المتصفح المحسن */
.browser-bar {
    background: linear-gradient(90deg, #2c3e50, #34495e);
    padding: 12px 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 15px rgba(0,0,0,0.2);
    border-bottom: 2px solid #3498db;
}

.browser-bar img {
    width: 20px;
    margin-left: 12px;
    filter: brightness(1.2);
}

.browser-bar button {
    margin: 0 6px;
    border: none;
    background: rgba(255,255,255,0.1);
    font-size: 18px;
    cursor: pointer;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.browser-bar button:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.1);
}

.browser-bar span {
    color: white;
    font-weight: bold;
    font-size: 16px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* الحاوية الرئيسية */
.container {
    display: flex;
    height: calc(100vh - 60px);
    gap: 20px;
    padding: 20px;
}

/* اللوحات المحسنة */
.panel {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.panel:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.panel h2 {
    margin-bottom: 25px;
    font-size: 24px;
    color: #2c3e50;
    text-align: center;
    background: linear-gradient(45deg, #3498db, #9b59b6);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

/* الشبكة المحسنة */
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* البطاقات المحسنة */
.card {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #3498db, #9b59b6);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.card:hover::before {
    opacity: 0.1;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    border-color: #3498db;
}

.card img {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    margin-left: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.card:hover img {
    transform: rotate(5deg) scale(1.1);
}

.card span {
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
    transition: color 0.3s ease;
}

.card:hover span {
    color: #3498db;
}

/* زر إضافة المنصة المحسن */
.add-platform-btn {
    width: 100%;
    margin: 20px 0;
    padding: 15px 25px;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
    position: relative;
    overflow: hidden;
}

.add-platform-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.add-platform-btn:hover::before {
    left: 100%;
}

.add-platform-btn:hover {
    background: linear-gradient(45deg, #c0392b, #a93226);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.4);
}

.add-platform-btn:active {
    transform: translateY(-1px);
}

/* تحسينات إضافية للاستجابة */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        height: auto;
        padding: 10px;
    }
    
    .panel {
        margin-bottom: 20px;
    }
    
    .grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .card {
        padding: 15px;
    }
}

/* تأثيرات متحركة إضافية */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease forwards;
}

.card:nth-child(2) { animation-delay: 0.1s; }
.card:nth-child(3) { animation-delay: 0.2s; }
.card:nth-child(4) { animation-delay: 0.3s; }
.card:nth-child(5) { animation-delay: 0.4s; }
.card:nth-child(6) { animation-delay: 0.5s; }

/* شريط التمرير المخصص */
.panel::-webkit-scrollbar {
    width: 8px;
}

.panel::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 10px;
}

.panel::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3498db, #9b59b6);
    border-radius: 10px;
}

.panel::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2980b9, #8e44ad);
}

/* العنوان الرئيسي */
.main-header {
    text-align: center;
    padding: 40px 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin: 20px;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.main-header h1 {
    font-size: 32px;
    color: white;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.main-header h1 i {
    margin-left: 15px;
    color: #3498db;
}

.main-header p {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

/* رأس اللوحة */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(52, 152, 219, 0.2);
}

.account-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.account-avatar {
    font-size: 40px;
    color: #3498db;
}

.account-details h2 {
    margin: 0;
    font-size: 20px;
    background: none;
    -webkit-text-fill-color: #2c3e50;
    text-align: right;
}

.platform-count {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: normal;
}

.action-btn {
    background: rgba(52, 152, 219, 0.1);
    border: none;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    color: #3498db;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    transform: scale(1.1);
}

/* تحسين البطاقات */
.card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
}

.card-icon img {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.card-content {
    flex: 1;
    margin: 0 15px;
    text-align: right;
}

.platform-name {
    display: block;
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.platform-status {
    font-size: 12px;
    color: #27ae60;
    font-weight: normal;
}

.card-action {
    color: #3498db;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover .card-action {
    opacity: 1;
}

/* شريط المتصفح المحسن */
.browser-bar {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.1);
}

.url-bar {
    flex: 1;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 15px;
}

.url-bar i {
    color: #3498db;
}

/* النافذة المنبثقة */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.7);
    background: white;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0,0,0,0.3);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 90%;
    max-width: 500px;
}

.modal.active {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.modal-content {
    padding: 0;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px;
    border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 20px;
}

.modal-header h3 i {
    margin-left: 10px;
    color: #3498db;
}

.close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #95a5a6;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #ecf0f1;
    color: #e74c3c;
}

/* النموذج */
form {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 600;
}

.form-group label i {
    margin-left: 8px;
    color: #3498db;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    direction: ltr;
    text-align: left;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
}

.btn-cancel, .btn-submit {
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #ecf0f1;
    color: #7f8c8d;
}

.btn-cancel:hover {
    background: #d5dbdb;
}

.btn-submit {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
}

.btn-submit:hover {
    background: linear-gradient(45deg, #229954, #27ae60);
    transform: translateY(-2px);
}

.btn-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* التراكب */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 2000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid #27ae60;
}

.notification.error {
    border-left: 4px solid #e74c3c;
}

.notification i {
    font-size: 18px;
}

.notification.success i {
    color: #27ae60;
}

.notification.error i {
    color: #e74c3c;
}

/* تأثير التموج */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(52, 152, 219, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
