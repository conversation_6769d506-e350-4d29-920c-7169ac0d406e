# AnythingLLM - تقرير التشغيل والحالة
# ===============================================

## 📁 المسارات الرئيسية:
- المجلد الرئيسي: anything-llm
- مجلد Docker: anything-llm/docker
- ملف التكوين: anything-llm/docker/docker-compose.yml

## 🚀 أوامر التشغيل:
cd anything-llm/docker
docker-compose up -d

## 🔍 أوامر فحص الحالة:
docker ps                           # عرض الحاويات المشغلة
docker logs anythingllm            # عرض سجلات التطبيق
docker-compose down                 # إيقاف التطبيق

## 🌐 الوصول للتطبيق:
URL: http://localhost:3001

## ⚙️ إعدادات الحاوية:
- اسم الحاوية: anythingllm
- الصورة: mintplexlabs/anythingllm:latest
- المنفذ: 3001:3001
- الشبكة: anything-llm

## 📊 آخر تقرير حالة:
التاريخ: 2025-07-05 02:04
الحالة: ✅ يعمل بشكل طبيعي
المنفذ: ✅ 3001 متاح
قاعدة البيانات: ✅ SQLite متصلة
الاستجابة: ✅ HTTP 200 OK

## 🔧 استكشاف الأخطاء:
إذا لم يعمل التطبيق:
1. تحقق من تشغيل Docker: docker --version
2. تحقق من الحاويات: docker ps -a
3. راجع السجلات: docker logs anythingllm
4. أعد التشغيل: docker-compose restart

## 📝 ملاحظات:
- تم تعديل docker-compose.yml لاستخدام الصورة الجاهزة
- تم حذف إعدادات البناء لتسريع التشغيل
- الحاوية تعمل في وضع صحي (healthy)