<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الذكاء الاصطناعي | AI Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خطوط Google للعربية -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
        }
    </style>
</head>
<body>
    <!-- شريط المتصفح المحسن -->
    <div class="browser-bar">
        <button title="رجوع" class="nav-btn">
            <i class="fas fa-arrow-right"></i>
        </button>
        <button title="تقديم" class="nav-btn">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button title="إعادة تحميل" class="nav-btn">
            <i class="fas fa-sync-alt"></i>
        </button>
        <button title="الرئيسية" class="nav-btn">
            <i class="fas fa-home"></i>
        </button>
        <div class="url-bar">
            <i class="fas fa-robot"></i>
            <span>لوحة تحكم الذكاء الاصطناعي - AI Dashboard</span>
        </div>
        <button title="إعدادات" class="nav-btn">
            <i class="fas fa-cog"></i>
        </button>
    </div>

    <!-- العنوان الرئيسي -->
    <div class="main-header">
        <h1>
            <i class="fas fa-brain"></i>
            لوحة تحكم منصات الذكاء الاصطناعي
        </h1>
        <p>وصول سريع لجميع منصات الذكاء الاصطناعي المفضلة لديك</p>
    </div>

    <!-- الحاوية الرئيسية -->
    <div class="container">
        {% for acct in accounts %}
        <div class="panel" data-account="{{ loop.index }}">
            <!-- رأس اللوحة -->
            <div class="panel-header">
                <div class="account-info">
                    <div class="account-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="account-details">
                        <h2>{{ acct.email }}</h2>
                        <span class="platform-count">{{ acct.platforms|length }} منصة متاحة</span>
                    </div>
                </div>
                <div class="panel-actions">
                    <button class="action-btn" title="إعدادات الحساب">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- شبكة المنصات -->
            <div class="grid">
                {% for p in acct.platforms %}
                <a class="card" href="{{ p.url }}" target="_blank" data-platform="{{ p.name }}">
                    <div class="card-icon">
                        <img src="{{ p.icon }}" alt="{{ p.name }}" loading="lazy">
                    </div>
                    <div class="card-content">
                        <span class="platform-name">{{ p.name }}</span>
                        <span class="platform-status">متاح</span>
                    </div>
                    <div class="card-action">
                        <i class="fas fa-external-link-alt"></i>
                    </div>
                </a>
                {% endfor %}
            </div>

            <!-- زر إضافة منصة جديدة -->
            <button class="add-platform-btn" onclick="openAddPlatformModal({{ loop.index0 }})">
                <i class="fas fa-plus"></i>
                <span>إضافة منصة جديدة</span>
            </button>
        </div>
        {% endfor %}
    </div>

    <!-- نافذة إضافة منصة -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>
                    <i class="fas fa-plus-circle"></i>
                    إضافة منصة جديدة
                </h3>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="platformForm">
                <div class="form-group">
                    <label for="platformName">
                        <i class="fas fa-tag"></i>
                        اسم المنصة
                    </label>
                    <input type="text" id="platformName" placeholder="مثال: ChatGPT Pro" required>
                </div>
                <div class="form-group">
                    <label for="platformUrl">
                        <i class="fas fa-link"></i>
                        رابط المنصة
                    </label>
                    <input type="url" id="platformUrl" placeholder="https://example.com" required>
                </div>
                <div class="form-group">
                    <label for="platformIcon">
                        <i class="fas fa-image"></i>
                        رابط الأيقونة
                    </label>
                    <input type="url" id="platformIcon" placeholder="https://example.com/icon.png" required>
                </div>
                <div class="form-actions">
                    <button type="button" onclick="closeModal()" class="btn-cancel">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn-submit">
                        <i class="fas fa-plus"></i>
                        إضافة المنصة
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- تراكب الخلفية -->
    <div id="overlay" class="overlay" onclick="closeModal()"></div>

    <!-- JavaScript محسن -->
    <script>
        let currentAccountIndex = 0;

        // تأثير التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير تدريجي للبطاقات
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // تأثير الهوفر للبطاقات
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.03)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // فتح نافذة إضافة المنصة
        function openAddPlatformModal(accountIndex = 0) {
            currentAccountIndex = accountIndex;
            document.getElementById('modal').classList.add('active');
            document.getElementById('overlay').classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // التركيز على أول حقل
            setTimeout(() => {
                document.getElementById('platformName').focus();
            }, 300);
        }

        // إغلاق النافذة
        function closeModal() {
            document.getElementById('modal').classList.remove('active');
            document.getElementById('overlay').classList.remove('active');
            document.body.style.overflow = 'auto';
            
            // إعادة تعيين النموذج
            document.getElementById('platformForm').reset();
        }

        // إرسال النموذج
        document.getElementById('platformForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const platformName = document.getElementById('platformName').value;
            const platformUrl = document.getElementById('platformUrl').value;
            const platformIcon = document.getElementById('platformIcon').value;

            if (platformName && platformUrl && platformIcon) {
                // إضافة مؤشر التحميل
                const submitBtn = document.querySelector('.btn-submit');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
                submitBtn.disabled = true;

                fetch("/add_platform", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ 
                        name: platformName, 
                        url: platformUrl, 
                        icon: platformIcon,
                        accountIndex: currentAccountIndex
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إشعار نجاح
                        showNotification('تمت إضافة المنصة بنجاح!', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showNotification('حدث خطأ أثناء إضافة المنصة.', 'error');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في الاتصال.', 'error');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
            }
        });

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                <span>${message}</span>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // إغلاق النافذة بالـ ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // تأثير النقر على البطاقات
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', function(e) {
                // تأثير التموج
                const ripple = document.createElement('div');
                ripple.className = 'ripple';
                this.appendChild(ripple);
                
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    </script>
</body>
</html>
