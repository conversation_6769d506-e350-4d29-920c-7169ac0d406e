{"name": "AI Dev Assistant - Economic Workflow", "nodes": [{"parameters": {}, "id": "23a73cde-801e-4537-817a-24157d602388", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"path": "a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22", "options": {}}, "id": "e4414f6e-1d54-469b-9c71-33161c272a51", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [460, 300], "webhookId": "a9d7a22e-1c6e-4f7f-a63e-22e1b1d84b22"}, {"parameters": {"model": "gemini-pro", "prompt": "أنا مطور برامج وأحتاج إلى بناء الميزة التالية:\n\n{{ $json.body.feature_request }}\n\nمهمتك: قم بالبحث في الإنترنت عن أفضل الممارسات لبناء هذه الميزة، ثم قدم لي خطة تنفيذ مفصلة ومقسمة إلى خطوات واضحة للمطورين."}, "id": "413f283c-623e-469d-8959-548c414925e0", "name": "Google Gemini", "type": "n8n-nodes-base.googleGemini", "typeVersion": 1, "position": [680, 300], "credentials": {"googleGeminiApi": {"id": "YOUR_GEMINI_CREDENTIAL_ID", "name": "Google Gemini API account"}}}, {"parameters": {"baseURL": "http://host.docker.internal:11434", "model": "llama3:8b", "prompt": "بناءً على الخطة التالية التي وضعها الخبير:\n\n{{ $('Google Gemini').json.response }}\n\nمهمتك: اكتب مسودة أولية للكود (HTML, CSS, JS, أو Python حسب الحاجة) لتنفيذ هذه الخطة. ركز على بناء هيكل الكود بشكل واضح."}, "id": "87c4760a-a92c-4743-9a3d-a41785501869", "name": "Ollama", "type": "n8n-nodes-base.ollama", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"fileName": "={{ '/data/output/' + ($json.body.feature_name || 'feature') + '.md' }}", "data": "={{ $('Ollama').json.response }}"}, "id": "18f9c063-8a35-4303-9d93-376c257b420f", "name": "Write Binary File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 2, "position": [1120, 300]}], "connections": {"Webhook": {"main": [[{"node": "Google Gemini", "type": "main", "index": 0}]]}, "Google Gemini": {"main": [[{"node": "Ollama", "type": "main", "index": 0}]]}, "Ollama": {"main": [[{"node": "Write Binary File", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "createdAt": "2025-07-05T12:00:00.000Z", "updatedAt": "2025-07-05T12:00:00.000Z", "id": "ai-dev-assistant-workflow"}