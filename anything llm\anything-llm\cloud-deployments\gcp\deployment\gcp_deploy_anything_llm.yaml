resources:  
  - name: anything-llm-instance  
    type: compute.v1.instance  
    properties:  
      zone: us-central1-a  
      machineType: zones/us-central1-a/machineTypes/n1-standard-1  
      disks:  
        - deviceName: boot  
          type: PERSISTENT  
          boot: true  
          autoDelete: true  
          initializeParams:  
            sourceImage: projects/ubuntu-os-cloud/global/images/family/ubuntu-2004-lts  
            diskSizeGb: 10  
      networkInterfaces:  
        - network: global/networks/default  
          accessConfigs:  
            - name: External NAT  
              type: ONE_TO_ONE_NAT  
      metadata:  
        items:  
          - key: startup-script  
            value: |  
              #!/bin/bash  
              # check output of userdata script with sudo tail -f /var/log/cloud-init-output.log  

              sudo apt-get update  
              sudo apt-get install -y docker.io  
              sudo usermod -a -G docker ubuntu
              sudo systemctl enable docker  
              sudo systemctl start docker  

              mkdir -p /home/<USER>
              touch /home/<USER>/.env
              sudo chown -R ubuntu:ubuntu /home/<USER>
               
              sudo docker pull mintplexlabs/anythingllm
              sudo docker run -d -p 3001:3001 --cap-add SYS_ADMIN -v /home/<USER>/app/server/storage -v /home/<USER>/.env:/app/server/.env -e STORAGE_DIR="/app/server/storage" mintplexlabs/anythingllm
              echo "Container ID: $(sudo docker ps --latest --quiet)"  

              export ONLINE=$(curl -Is http://localhost:3001/api/ping | head -n 1|cut -d$' ' -f2)  
              echo "Health check: $ONLINE"  

              echo "Setup complete! AnythingLLM instance is now online!"  

