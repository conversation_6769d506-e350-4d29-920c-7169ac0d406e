from flask import Flask, render_template, request, jsonify

app = Flask(__name__)

# بيانات الحسابات مع جميع منصات الذكاء الاصطناعي
accounts = [
    {
        "email": "<EMAIL>",
        "platforms": [
            {
                "name": "ChatGPT",
                "url": "https://chat.openai.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/10691/********.png"
                )
            },
            {
                "name": "OpenAI Playground",
                "url": "https://platform.openai.com/playground",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/5968/5968350.png"
                )
            },
            {
                "name": "Google Bard",
                "url": "https://bard.google.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/2504/2504739.png"
                )
            },
            {
                "name": "Google Gemini",
                "url": "https://gemini.google.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/5968/5968885.png"
                )
            },
            {
                "name": "Anthropic Claude",
                "url": "https://claude.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/6840/6840478.png"
                )
            },
            {
                "name": "Bing Chat",
                "url": "https://www.bing.com/chat",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/888/888857.png"
                )
            },
            {
                "name": "Hugging Face",
                "url": "https://huggingface.co/spaces",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/4700/4700741.png"
                )
            },
            {
                "name": "Perplexity AI",
                "url": "https://www.perplexity.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/747/747376.png"
                )
            },
            {
                "name": "DeepSeek Chat",
                "url": "https://chat.deepseek.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/10691/10691903.png"
                )
            },
            {
                "name": "Mistral AI",
                "url": "https://mistral.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/10691/10691904.png"
                )
            }
        ]
    },
    {
        "email": "<EMAIL>",
        "platforms": [
            {
                "name": "YouChat",
                "url": "https://you.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/5968/5968900.png"
                )
            },
            {
                "name": "Jasper AI",
                "url": "https://www.jasper.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/919/919825.png"
                )
            },
            {
                "name": "Chatsonic",
                "url": "https://writesonic.com/chat",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/5968/5968672.png"
                )
            },
            {
                "name": "Llama 2 Hub",
                "url": "https://huggingface.co/meta-llama",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/2504/2504735.png"
                )
            },
            {
                "name": "Claude 2",
                "url": "https://claude.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/6840/6840478.png"
                )
            },
            {
                "name": "Bing Chat",
                "url": "https://www.bing.com/chat",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/888/888857.png"
                )
            },
            {
                "name": "Hugging Face",
                "url": "https://huggingface.co/spaces",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/4700/4700741.png"
                )
            },
            {
                "name": "Perplexity AI",
                "url": "https://www.perplexity.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/747/747376.png"
                )
            },
            {
                "name": "Cohere",
                "url": "https://cohere.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/10691/10691905.png"
                )
            },
            {
                "name": "Inflection AI",
                "url": "https://inflection.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/10691/10691906.png"
                )
            }
        ]
    },
    {
        "email": "<EMAIL>",
        "platforms": [
            {
                "name": "ChatGPT",
                "url": "https://chat.openai.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/10691/********.png"
                )
            },
            {
                "name": "OpenAI Playground",
                "url": "https://platform.openai.com/playground",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/5968/5968350.png"
                )
            },
            {
                "name": "Google Bard",
                "url": "https://bard.google.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/2504/2504739.png"
                )
            },
            {
                "name": "Google Gemini",
                "url": "https://gemini.google.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/5968/5968885.png"
                )
            },
            {
                "name": "Anthropic Claude",
                "url": "https://claude.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/6840/6840478.png"
                )
            },
            {
                "name": "Bing Chat",
                "url": "https://www.bing.com/chat",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/888/888857.png"
                )
            },
            {
                "name": "Hugging Face",
                "url": "https://huggingface.co/spaces",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/4700/4700741.png"
                )
            },
            {
                "name": "Perplexity AI",
                "url": "https://www.perplexity.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/747/747376.png"
                )
            },
            {
                "name": "Meta AI",
                "url": "https://ai.meta.com",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/10691/********.png"
                )
            },
            {
                "name": "Stability AI",
                "url": "https://stability.ai",
                "icon": (
                    "https://cdn-icons-png.flaticon.com/"
                    "512/10691/********.png"
                )
            }
        ]
    }
]

@app.route("/")
def index():
    return render_template("template.html", accounts=accounts)

@app.route("/add_platform", methods=["POST"])
def add_platform():
    data = request.json
    # افتراضياً، نضيف المنصة إلى أول حساب
    accounts[0]["platforms"].append(
        {
            "name": data["name"],
            "url": data["url"],
            "icon": data["icon"]
        }
    )
    return jsonify({"success": True})

if __name__ == "__main__":
    app.run(debug=True)
