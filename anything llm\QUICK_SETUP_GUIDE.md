# 🚀 دليل الإعداد السريع
## Quick Setup Guide - النظام الذكي المتكامل

## 📋 الخطوات الأساسية (5 دقائق)

### 1. تثبيت المتطلبات تلقائياً ✨

#### الطريقة الأولى: من VS Code (الأسهل)
```
1. اضغط Ctrl+Shift+P
2. اكتب: Tasks: Run Task
3. اختر: 📦 تثبيت المتطلبات
4. انتظر اكتمال التثبيت
```

#### الطريقة الثانية: من الطرفية
```bash
python install_dependencies.py
```

#### الطريقة الثالثة: يدوياً
```bash
pip install -r requirements.txt
```

### 2. اختبار النظام 🧪

#### من VS Code:
```
Ctrl+Shift+P > Tasks: Run Task > 🧪 اختبار النظام
```

#### من الطرفية:
```bash
python test_system.py
python vscode_ai_controller.py --check-status
```

### 3. تشغيل النظام 🚀

#### من VS Code:
```
Ctrl+Shift+P > Tasks: Run Task > 🚀 تشغيل النظام الذكي
```

#### من الطرفية:
```bash
docker-compose up -d
```

### 4. أول استخدام 🎯

#### اختبار سريع:
```bash
python vscode_ai_controller.py "اكتب دالة Python لحساب المربع"
```

#### فتح الواجهات:
- **AnythingLLM**: http://localhost:3001
- **n8n**: http://localhost:5678 (admin/admin123)

## 🔧 استكشاف الأخطاء

### مشكلة: Python غير موجود
```bash
# تحقق من تثبيت Python
python --version
# أو
python3 --version
```

### مشكلة: pip غير موجود
```bash
# تحديث pip
python -m pip install --upgrade pip
```

### مشكلة: Docker غير مُفعل
```bash
# تأكد من تشغيل Docker Desktop
docker --version
docker info
```

### مشكلة: المتطلبات لا تُثبت
```bash
# تثبيت المكتبات الأساسية فقط
pip install requests python-dotenv colorama

# ثم جرب النظام
python vscode_ai_controller.py --check-status
```

## 📦 المكتبات المطلوبة

### أساسية (مطلوبة):
- ✅ `requests` - للاتصال بالخدمات
- ✅ `python-dotenv` - لإدارة متغيرات البيئة
- ✅ `colorama` - للألوان في الطرفية

### متقدمة (اختيارية):
- ☁️ `google-cloud-aiplatform` - لـ Google Cloud
- ☁️ `vertexai` - لـ Gemini Pro
- 🎨 `rich` - لواجهة أجمل
- 🔧 `flask` - للوحة التحكم

## 🎯 خيارات التشغيل

### خيار 1: الحد الأدنى (بدون Docker)
```bash
# تثبيت المكتبات الأساسية فقط
pip install requests python-dotenv

# استخدام النظام للمهام البسيطة
python vscode_ai_controller.py "سؤالك هنا"
```

### خيار 2: كامل محلي (مع Docker)
```bash
# تثبيت كل شيء
python install_dependencies.py

# تشغيل Docker
docker-compose up -d

# استخدام كامل
python vscode_ai_controller.py --check-status
```

### خيار 3: متقدم مع Google Cloud
```bash
# تثبيت مكتبات Google Cloud
pip install google-cloud-aiplatform vertexai

# إعداد Google Cloud
gcloud auth login
gcloud config set project ai-integrated-system-2025

# استخدام قوة السحابة
python vscode_ai_controller.py "تحليل معقد"
```

## 🆘 المساعدة السريعة

### أوامر مفيدة:
```bash
# فحص Python
python --version

# فحص pip
pip --version

# فحص Docker
docker --version

# فحص المكتبات
pip list | grep requests

# إعادة تثبيت
pip uninstall requests
pip install requests

# مسح cache
pip cache purge
```

### ملفات مهمة:
- 📄 `requirements.txt` - قائمة المتطلبات
- 📄 `install_dependencies.py` - مثبت تلقائي
- 📄 `test_system.py` - اختبار النظام
- 📄 `vscode_ai_controller.py` - المتحكم الرئيسي

## 🎉 علامات النجاح

### ✅ تثبيت ناجح:
```
✅ Python 3.x.x
✅ تم تثبيت requests
✅ تم تثبيت python-dotenv  
✅ جميع المكتبات الأساسية متاحة
🎉 النظام جاهز للاستخدام!
```

### ✅ تشغيل ناجح:
```
🔍 حالة النظام:
✅ n8n: متصل
✅ ollama: متصل  
✅ anything_llm: متصل

🤖 النماذج المتاحة في Ollama: ['llama3:8b', 'mistral']
```

## 📞 الحصول على المساعدة

### إذا واجهت مشاكل:
1. 🔍 راجع رسائل الخطأ بعناية
2. 🧪 شغّل `python test_system.py`
3. 📋 تأكد من تثبيت Python و Docker
4. 🔄 أعد تشغيل VS Code
5. 💬 اطلب المساعدة مع تفاصيل الخطأ

### مسارات الملفات المهمة:
- **المجلد الحالي**: `C:\Users\<USER>\anything llm`
- **النظام المتطور**: `C:\Users\<USER>\model_mix`
- **الإعدادات**: `.vscode/tasks.json`

---

**🚀 بدقائق معدودة ستكون جاهزاً لاستخدام أقوى نظام ذكاء اصطناعي محلي!**
